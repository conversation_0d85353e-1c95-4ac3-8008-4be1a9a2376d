﻿---
ID: "95b15122-703c-45b4-a46a-22699ee8ece4"
Parent: "0f644cda-d3bf-4e3d-80bc-99a324e896d6"
Template: "04646a89-996f-4ee7-878a-ffdbf1f0ef0d"
Path: /sitecore/layout/Renderings/Project/XMCMyaccount/Payment/SplitPayment/SplitPaymentAmount
SharedFields:
- ID: "037fe404-dd19-4bf7-8e30-4dadf68b27b0"
  Hint: componentName
  Value: SplitPaymentAmount
- ID: "069a8361-b1cd-437c-8c32-a3be78941446"
  Hint: Placeholders
  Value: "{995DA88F-9AE7-5F82-A0A3-A752442833F9}"
- ID: "06d5295c-ed2f-4a54-9bf2-26228d113318"
  Hint: __Icon
  Value: Office/32x32/bowling_pins.png
- ID: "17bb046a-a32a-41b3-8315-81217947611b"
  Hint: ComponentQuery
  Value: |
    query SplitPaymentAmountQuery($datasource: String!, $language: String!) {
       item(path: $datasource, language: $language) {
        heading: field(name: "heading") {
          value
        }
        AmountDueText: field(name: "AmountDueText") {
          value
        }
        EnterAmountText: field(name: "EnterAmountText") {
          value
        }
        SplitEvenlyText: field(name: "SplitEvenlyText") {
          value
        }
        PaymentAmountText: field(name: "PaymentAmountText") {
          value
        }
        RemainingBalanceText: field(name: "RemainingBalanceText") {
          value
        }
        ContinuePaymentReviewButtonText: field(name: "ContinuePaymentReviewButtonText") {
          value
        }
        BackToSinglePaymentText: field(name: "BackToSinglePaymentText") {
          value
        }
        CancelPaymentText: field(name: "CancelPaymentText") {
          value
        }
        CardEndingText: field(name: "CardEndingText") {
          value
        }
        AccountEndingText: field(name: "AccountEndingText") {
          value
        }
        BackToSinglePaymentLink: field(name: "BackToSinglePaymentLink") {
          jsonValue
        }
        CancelPaymentLink: field(name: "CancelPaymentLink") {
          jsonValue
        }
        ContinuePaymentLink: field(name: "ContinuePaymentLink") {
          jsonValue
        }
        SplitPaymentMinimumAmount: field(name: "SplitPaymentMinimumAmount") {
          value
        }
        MinimumAmountErrorMessage: field(name: "MinimumAmountErrorMessage") {
          value
        }
        PaymentDate: field(name: "PaymentDate") {
          value
        }
      }
    }
    
- ID: "308e88f5-cd6e-4f8f-bafe-95a47dedefdc"
  Hint: Editable
  Value: 
- ID: "c39a90ce-0035-41bb-90f6-3c8a6ea87797"
  Hint: AddFieldEditorButton
  Value: 
- ID: "f172b787-7b88-4bd5-ae4d-6308e102ef11"
  Hint: Enable Datasource Query
  Value: 
Languages:
- Language: en
  Versions:
  - Version: 1
    Fields:
    - ID: "8cdc337e-a112-42fb-bbb4-4143751e123f"
      Hint: __Revision
      Value: "a95f2937-c67e-490e-9fc0-5c49745a12ee"
