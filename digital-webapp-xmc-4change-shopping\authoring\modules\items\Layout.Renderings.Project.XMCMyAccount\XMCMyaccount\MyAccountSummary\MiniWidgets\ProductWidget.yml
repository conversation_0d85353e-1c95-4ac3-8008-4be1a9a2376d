﻿---
ID: "0ca9203c-4c40-4f63-a8c8-2bd7c90ba358"
Parent: "36248ccb-a0f9-45f8-b02f-fc6ad999ffd3"
Template: "04646a89-996f-4ee7-878a-ffdbf1f0ef0d"
Path: /sitecore/layout/Renderings/Project/XMCMyaccount/MyAccountSummary/MiniWidgets/ProductWidget
SharedFields:
- ID: "037fe404-dd19-4bf7-8e30-4dadf68b27b0"
  Hint: componentName
  Value: ProductWidget
- ID: "06d5295c-ed2f-4a54-9bf2-26228d113318"
  Hint: __Icon
  Value: office/32x32/bacon.png
- ID: "17bb046a-a32a-41b3-8315-81217947611b"
  Hint: ComponentQuery
  Value: |
    query ProductWidgetQuery($datasource: String!, $language: String!) {
      item(path: $datasource, language: $language) {
        NameText: field(name: "NameText") {
          value
        }
        TotalSavingsText: field(name: "TotalSavingsText") {
          value
        }
        TotalSavingsSubText: field(name: "TotalSavingsSubText") {
          value
        }
        SavingsDetailButtonText: field(name: "SavingsDetailButtonText") {
          value
        }
        SavingsDetailButtonLink: field(name: "SavingsDetailButtonLink") {
          jsonValue
        }
        GetStartedText: field(name: "GetStartedText") {
          value
        }
        FreeEvChargingUsageText: field(name: "FreeEvChargingUsageText") {
          value
        }
        SavingsMonth: field(name: "SavingsMonth") {
          value
        }
        FreeMilesText: field(name: "FreeMilesText") {
          value
        }
        TotalPlanSavingsText: field(name: "TotalPlanSavingsText") {
          value
        }
        FreeNightsUsageText: field(name: "FreeNightsUsageText") {
          value
        }
        SavingsFromLastBillingCycleText: field(name: "SavingsFromLastBillingCycleText") {
          value
        }
        DaytimeUsageOffText: field(name: "DaytimeUsageOffText") {
          value
        }
        SavedMostTimingText: field(name: "SavedMostTimingText") {
          value
        }
        MonthlySavingsText: field(name: "MonthlySavingsText") {
          value
        }
        ProductType1: field(name: "ProductType1") {
          value
        }
        ProductType2: field(name: "ProductType2") {
          value
        }
        ProductType3: field(name: "ProductType3") {
          value
        }
        ProductType4: field(name: "ProductType4") {
          value
        }
        ProductType5: field(name: "ProductType5") {
          value
        }
        ProductType6: field(name: "ProductType6") {
          value
        }
        ProductType7: field(name: "ProductType7") {
          value
        }
        ProductType8: field(name: "ProductType8") {
          value
        }
        SummerPassIcon: field(name: "SummerPassIcon") {
          jsonValue
        }
        DayTimePassIcon: field(name: "DayTimePassIcon") {
          jsonValue
        }
        EVMilesIcon: field(name: "EVMilesIcon") {
          jsonValue
        }
        FreeNightsSolarDaysIcon: field(name: "FreeNightsSolarDaysIcon") {
          jsonValue
        }
        SeasonPassIcon: field(name: "SeasonPassIcon") {
          jsonValue
        }
      }
    }
Languages:
- Language: en
  Versions:
  - Version: 1
    Fields:
    - ID: "8cdc337e-a112-42fb-bbb4-4143751e123f"
      Hint: __Revision
      Value: "d8466e8a-cddc-4638-93a4-ee808f28bdb1"
