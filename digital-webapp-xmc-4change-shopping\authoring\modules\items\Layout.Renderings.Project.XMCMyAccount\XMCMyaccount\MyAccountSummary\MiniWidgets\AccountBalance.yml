﻿---
ID: "********-df98-451d-a8d3-e2d7559f17e9"
Parent: "36248ccb-a0f9-45f8-b02f-fc6ad999ffd3"
Template: "04646a89-996f-4ee7-878a-ffdbf1f0ef0d"
Path: /sitecore/layout/Renderings/Project/XMCMyaccount/MyAccountSummary/MiniWidgets/AccountBalance
SharedFields:
- ID: "037fe404-dd19-4bf7-8e30-4dadf68b27b0"
  Hint: componentName
  Value: AccountBalance
- ID: "06d5295c-ed2f-4a54-9bf2-26228d113318"
  Hint: __Icon
  Value: Office/32x32/blackboard.png
- ID: "17bb046a-a32a-41b3-8315-81217947611b"
  Hint: ComponentQuery
  Value: |
    query AccountBalanceQuery($datasource: String!, $language: String!) {
      item(path: $datasource, language: $language) {
        AccountBalance: field(name: "AccountBalance") {
          value
        }
        DueDate: field(name: "DueDate") {
          value
        }
        ViewBill: field(name: "ViewBill") {
          value
        }
        LastPayment: field(name: "LastPayment") {
          value
        }
        PaymentDate: field(name: "PaymentDate") {
          value
        }
        MakeAPayment: field(name: "MakeAPayment") {
          value
        }
        AutopayScheduledText: field(name: "AutopayScheduledText") {
          value
        }
        SignUpForAutoPay: field(name: "SignUpForAutoPay") {
          value
        }
        NeedMoreTimeToPay: field(name: "NeedMoreTimeToPay") {
          value
        }
        OneTimePaymentButtonText: field(name: "OneTimePaymentButtonText") {
          value
        }
        OneTimePaymentLink: field(name: "OneTimePaymentLink") {
          jsonValue
        }
        AutopaySettingsButtonText: field(name: "AutopaySettingsButtonText") {
          value
        }
        MakeAPaymentLink: field(name: "MakeAPaymentLink") {
          jsonValue
        }
        SignUpForAutoPayLink: field(name: "SignUpForAutoPayLink") {
          jsonValue
        }
        NeedMoreTimeToPayLink: field(name: "NeedMoreTimeToPayLink") {
          jsonValue
        }
        AccountZeroBalanceDescrition: field(name: "AccountZeroBalanceDescrition") {
          value
        }
        AccountBalanceAllSetText: field(name: "AccountBalanceAllSetText") {
          value
        }
        AccountBalanceDueText: field(name: "AccountBalanceDueText") {
          value
        }
        PastDueMessage: field(name: "PastDueMessage") {
          value
        }
        PendingDisconnectMessage: field(name: "PendingDisconnectMessage") {
          value
        }
        DisconnectMessage: field(name: "DisconnectMessage") {
          value
        }
        PendingDisconnectDateText: field(name: "PendingDisconnectDateText") {
          value
        }
        ErrorText: field(name: "ErrorText") {
          value
        }
        NoDataErrorMessage: field(name: "NoDataErrorMessage") {
          value
        }
        Usage: field(name: "Usage") {
          value
        }
        Unit: field(name: "Unit") {
          value
        }
        DateFormat: field(name: "DateFormat") {
          value
        }
        AccountBalanceDetail: field(name: "AccountBalanceDetail") {
          value
        }
        PendingDisconnectMessageTooltip: field(name: "PendingDisconnectMessageTooltip") {
          value
        }
        PaymybillLink: field(name: "PaymybillLink") {
          jsonValue
        }
        NextPayment: field(name: "NextPayment") {
          value
        }
        NextPaymentDate: field(name: "NextPaymentDate") {
          value
        }
      }
    }
- ID: "a77e8568-1ab3-44f1-a664-b7c37ec7810d"
  Hint: Parameters Template
  Value: "{********-21D3-4420-9B6F-C215E3BABF4E}"
- ID: "dbbbeca1-21c7-4906-9dd2-493c1efa59a2"
  Hint: __Shared revision
  Value: "7363da38-8dde-4172-96da-173f68945957"
Languages:
- Language: en
  Versions:
  - Version: 1
    Fields:
    - ID: "25bed78c-4957-4165-998a-ca1b52f67497"
      Hint: __Created
      Value: 20240207T065940Z
    - ID: "5dd74568-4d4b-44c1-b513-0af5f4cda34f"
      Hint: __Created by
      Value: |
        sitecore\Admin
    - ID: "8cdc337e-a112-42fb-bbb4-4143751e123f"
      Hint: __Revision
      Value: "99d03b02-109a-42f4-99a4-eb6562343222"
    - ID: "badd9cf9-53e0-4d0c-bcc0-2d784c282f6a"
      Hint: __Updated by
      Value: |
        sitecore\<EMAIL>
    - ID: "d9cf14b1-fa16-4ba6-9288-e8a174d4d522"
      Hint: __Updated
      Value: 20250418T074640Z
