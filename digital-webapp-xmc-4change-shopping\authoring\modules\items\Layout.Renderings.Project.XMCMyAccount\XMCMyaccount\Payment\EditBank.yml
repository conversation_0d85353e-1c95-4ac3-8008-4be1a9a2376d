﻿---
ID: "e239d3c4-ab8e-47d0-be90-7bb156fc50c9"
Parent: "e13e91f1-a791-4889-ba15-5a8aa1f9aec3"
Template: "04646a89-996f-4ee7-878a-ffdbf1f0ef0d"
Path: /sitecore/layout/Renderings/Project/XMCMyaccount/Payment/EditBank
SharedFields:
- ID: "037fe404-dd19-4bf7-8e30-4dadf68b27b0"
  Hint: componentName
  Value: EditBank
- ID: "06d5295c-ed2f-4a54-9bf2-26228d113318"
  Hint: __Icon
  Value: Office/32x32/moneybag.png
- ID: "17bb046a-a32a-41b3-8315-81217947611b"
  Hint: ComponentQuery
  Value: |
    query EditBankQuery($datasource: String!, $language: String!) {
      item(path: $datasource, language: $language) {
        AddEditBankAccountTitle: field(name: "AddEditBankAccountTitle") {
          value
        }
        AccountEndingText: field(name: "AccountEndingText") {
          value
        }
        NickNamePaymentMethodText: field(name: "NickNamePaymentMethodText") {
          value
        }
        AccountHolderNameText: field(name: "AccountHolderNameText") {
          value
        }
        RoutingNumberText: field(name: "RoutingNumberText") {
          value
        }
        BankAccNumberText: field(name: "BankAccNumberText") {
          value
        }
        SaveChangesText: field(name: "SaveChangesText") {
          value
        }
        CancelChangesText: field(name: "CancelChangesText") {
          value
        }
        DeletePaymentMethodBtnText: field(name: "DeletePaymentMethodBtnText") {
          value
        }
        BackButton: field(name: "BackButton") {
          value
        }
        ScheduledPaymentErrorText: field(name: "ScheduledPaymentErrorText") {
          value
        }
        AutoPayErrorText: field(name: "AutoPayErrorText") {
          value
        }
        EditTextForApp: field(name: "EditTextForApp") {
          value
        }
         BackButtonLink: field(name: "BackButtonLink") {
          jsonValue
        }
        DeleteTitle: field(name: "DeleteTitle") {
          value
        }
        DeleteDescription: field(name: "DeleteDescription") {
          value
        }
        DeleteItem: field(name: "DeleteItem") {
          value
        }
        ConfirmText: field(name: "ConfirmText") {
          value
        }
        CancelText: field(name: "CancelText") {
          value
        }
       
       
      }
    }
    
- ID: "308e88f5-cd6e-4f8f-bafe-95a47dedefdc"
  Hint: Editable
  Value: 
- ID: "c39a90ce-0035-41bb-90f6-3c8a6ea87797"
  Hint: AddFieldEditorButton
  Value: 
- ID: "f172b787-7b88-4bd5-ae4d-6308e102ef11"
  Hint: Enable Datasource Query
  Value: 
Languages:
- Language: en
  Versions:
  - Version: 1
    Fields:
    - ID: "8cdc337e-a112-42fb-bbb4-4143751e123f"
      Hint: __Revision
      Value: "9b8f92bc-c072-4732-ad4c-b6bd97c3a272"
