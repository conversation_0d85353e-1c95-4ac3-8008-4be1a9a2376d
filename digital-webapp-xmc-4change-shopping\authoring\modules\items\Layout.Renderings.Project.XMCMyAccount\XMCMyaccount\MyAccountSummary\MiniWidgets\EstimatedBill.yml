﻿---
ID: "dc6226c8-55c5-4a68-8331-2f542460aefe"
Parent: "36248ccb-a0f9-45f8-b02f-fc6ad999ffd3"
Template: "04646a89-996f-4ee7-878a-ffdbf1f0ef0d"
Path: /sitecore/layout/Renderings/Project/XMCMyaccount/MyAccountSummary/MiniWidgets/EstimatedBill
SharedFields:
- ID: "037fe404-dd19-4bf7-8e30-4dadf68b27b0"
  Hint: componentName
  Value: EstimatedBill
- ID: "06d5295c-ed2f-4a54-9bf2-26228d113318"
  Hint: __Icon
  Value: Office/32x32/money2.png
- ID: "17bb046a-a32a-41b3-8315-81217947611b"
  Hint: ComponentQuery
  Value: |
    query EstimatedBillQuery($datasource: String!, $language: String!) {
      item(path: $datasource, language: $language) {
        Title: field(name: "Title") {
          jsonValue
        }
        SubTitle: field(name: "SubTitle") {
          value
        }
        CompareCostUp: field(name: "CompareCostUp") {
          value
        }
        CompareCostDown: field(name: "CompareCostDown") {
          value
        }
        AMBTitle: field(name: "AMBTitle") {
          value
        }
        AMBSubTitle: field(name: "AMBSubTitle") {
          value
        }
        AMBCumulativeBalanceTitle: field(name: "AMBCumulativeBalanceTitle") {
          value
        }
        AMBCumulativeBalanceSubTitle: field(name: "AMBCumulativeBalanceSubTitle") {
          value
        }
        AMBCumulativeBalanceToolTip: field(name: "AMBCumulativeBalanceToolTip") {
          value
        }
        ErrorText: field(name: "ErrorText") {
          value
        }
        ComingSoonText: field(name: "ComingSoonText") {
          value
        }
      }
    }
Languages:
- Language: en
  Versions:
  - Version: 1
    Fields:
    - ID: "8cdc337e-a112-42fb-bbb4-4143751e123f"
      Hint: __Revision
      Value: "984897f8-efec-46a3-9f8f-75d05047a0ce"
