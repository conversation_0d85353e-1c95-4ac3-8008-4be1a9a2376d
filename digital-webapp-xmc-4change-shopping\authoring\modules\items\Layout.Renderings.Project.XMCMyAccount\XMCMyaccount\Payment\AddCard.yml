﻿---
ID: "c0f8107a-02ce-4b10-b400-98dc93ebbea3"
Parent: "e13e91f1-a791-4889-ba15-5a8aa1f9aec3"
Template: "04646a89-996f-4ee7-878a-ffdbf1f0ef0d"
Path: /sitecore/layout/Renderings/Project/XMCMyaccount/Payment/AddCard
SharedFields:
- ID: "037fe404-dd19-4bf7-8e30-4dadf68b27b0"
  Hint: componentName
  Value: AddCard
- ID: "06d5295c-ed2f-4a54-9bf2-26228d113318"
  Hint: __Icon
  Value: Office/32x32/money.png
- ID: "17bb046a-a32a-41b3-8315-81217947611b"
  Hint: ComponentQuery
  Value: |
    query AddCardQuery($datasource: String!, $language: String!) {
     item(path: $datasource, language: $language) {
        AddCreditOrDebitCardText: field(name: "AddCreditOrDebitCardText") {
          value
        }
        CardDetailsText: field(name: "CardDetailsText") {
          value
        }
        CardNumberText: field(name: "CardNumberText") {
          value
        }
        NameOnCardText: field(name: "NameOnCardText") {
          value
        }
        ExpDateText: field(name: "ExpDateText") {
          value
        }
        SecurityCodeText: field(name: "SecurityCodeText") {
          value
        }
        ZipcodeText: field(name: "ZipcodeText") {
          value
        }
        SavePaymentInfoText: field(name: "SavePaymentInfoText") {
          value
        }
        NicknameText: field(name: "NicknameText") {
          value
        }
        CardDefaultPaymentSavetext: field(name: "CardDefaultPaymentSavetext") {
          value
        }
        AddButton: field(name: "AddButton") {
          value
        }
        BackButton: field(name: "BackButton") {
          value
        }
        AddEditCreditOrDebitCardText: field(name: "AddEditCreditOrDebitCardText") {
          value
        }
        AllowOneTimePayment: field(name: "AllowOneTimePayment") {
          value
        }
        PaymentAmountText: field(name: "PaymentAmountText") {
          value
        }
        PaymentDateText: field(name: "PaymentDateText") {
          value
        }
        ContinuePaymentButtonText: field(name: "ContinuePaymentButtonText") {
          value
        }
        DefaultErrorMessage: field(name: "DefaultErrorMessage") {
          value
        }
        ReviewPaymentLink: field(name: "ReviewPaymentLink") {
          jsonValue
        }
        AMEXCardErrorMessage: field(name: "AMEXCardErrorMessage") {
          value
        }
        NickNameErrorMessage: field(name: "NickNameErrorMessage") {
          value
        }
        AccountAlreadyExistsErrorText: field(name: "AccountAlreadyExistsErrorText") {
          value
        }
        CardEndingText: field(name: "CardEndingText") {
          value
        }
        PaymentAddedTitle: field(name: "PaymentAddedTitle") {
          value
        }
        PaymentAddedMessage: field(name: "PaymentAddedMessage") {
          value
        }
        PaymentAddedButtonText: field(name: "PaymentAddedButtonText") {
          value
        }
      }
    }
    
- ID: "308e88f5-cd6e-4f8f-bafe-95a47dedefdc"
  Hint: Editable
  Value: 
- ID: "a77e8568-1ab3-44f1-a664-b7c37ec7810d"
  Hint: Parameters Template
  Value: 
- ID: "ba3f86a2-4a1c-4d78-b63d-91c2779c1b5e"
  Hint: __Sortorder
  Value: 300
- ID: "c39a90ce-0035-41bb-90f6-3c8a6ea87797"
  Hint: AddFieldEditorButton
  Value: 
- ID: "f172b787-7b88-4bd5-ae4d-6308e102ef11"
  Hint: Enable Datasource Query
  Value: 
Languages:
- Language: en
  Versions:
  - Version: 1
    Fields:
    - ID: "8cdc337e-a112-42fb-bbb4-4143751e123f"
      Hint: __Revision
      Value: "1c54690b-5882-4d30-9eaf-4173cf7c4ea4"
