﻿---
ID: "f526e0a9-260b-4e50-b65c-297747782a76"
Parent: "b7619265-dbc7-4ab5-9e19-7cddeea7abee"
Template: "04646a89-996f-4ee7-878a-ffdbf1f0ef0d"
Path: /sitecore/layout/Renderings/Project/XMCMyaccount/COA/AccountInformation
SharedFields:
- ID: "037fe404-dd19-4bf7-8e30-4dadf68b27b0"
  Hint: componentName
  Value: AccountInformation
- ID: "17bb046a-a32a-41b3-8315-81217947611b"
  Hint: ComponentQuery
  Value: |
    query AccountInformationQuery($datasource: String!, $language: String!) {
       item(path: $datasource, language: $language) {
        CreateYourAccountText: field(name: "CreateYourAccountText") {
          value
        }
        CreateYourAccountDescription: field(name: "CreateYourAccountDescription") {
          value
        }
        AccountPersonalInfoText: field(name: "AccountPersonalInfoText") {
          value
        }
        AccountNumberText: field(name: "AccountNumberText") {
          value
        }
        WheredoFindText: field(name: "WheredoFindText") {
          value
        }
        NextButtonText: field(name: "NextButtonText") {
          value
        }
        PersonalInfoVerificationText: field(name: "PersonalInfoVerificationText") {
          value
        }
        AccountNoText: field(name: "AccountNoText") {
          value
        }
        FirstNameText: field(name: "FirstNameText") {
          value
        }
        LastNameText: field(name: "LastNameText") {
          value
        }
        ForSecurityPurposeText: field(name: "ForSecurityPurposeText") {
          value
        }
        DateOfBirthText: field(name: "DateOfBirthText") {
          value
        }
        LastFourDigitText: field(name: "LastFourDigitText") {
          value
        }
        MothersMaidenNameText: field(name: "MothersMaidenNameText") {
          value
        }
        DriversLicenseNumberText: field(name: "DriversLicenseNumberText") {
          value
        }
        TaxIdText: field(name: "TaxIdText") {
          value
        }
        AccountNumberInvalid: field(name: "AccountNumberInvalid") {
          value
        }
        InvalidDOBSSN: field(name: "InvalidDOBSSN") {
          value
        }
        WheredoFindHREF: field(name: "WheredoFindHREF") {
          value
        }
        WhereToFindCA: field(name: "WhereToFindCA") {
          jsonValue
        }
        logininformationurl: field(name: "logininformationurl") {
          jsonValue
        }
        ContactCustomerCareText: field(name: "ContactCustomerCareText") {
          value
        }
        AccountNumberInvalidAccountMessage: field(name: "AccountNumberInvalidAccountMessage") {
          value
        }
        InvalidAccountTypeErrorMessage: field(name: "InvalidAccountTypeErrorMessage") {
          value
        }
        OnlineAccountAlreadyExistsMessage: field(name: "OnlineAccountAlreadyExistsMessage") {
          value
        }
        AccountNumberNotFoundErrorMessage: field(name: "AccountNumberNotFoundErrorMessage") {
          value
        }
        GenericeErrorMessage: field(name: "GenericeErrorMessage") {
          value
        }
        BrandIdCheckFailed: field(name: "BrandIdCheckFailed") {
          value
        }
        allFieldsUnavailableText: field(name: "allFieldsUnavailableText") {
          value
        }
        DOBValidationText: field(name: "DOBValidationText") {
          value
        }
        SSNValidationText: field(name: "SSNValidationText") {
          value
        }
        DLStates: field(name: "DLStates") {
          ... on MultilistField {
            targetItems {
              Title: field(name: "Title") {
                value
              }
    
            }
          }
        }
      }
    }
    
- ID: "dbbbeca1-21c7-4906-9dd2-493c1efa59a2"
  Hint: __Shared revision
  Value: "09f20ee1-34d3-4e30-9dfb-92c100ca3036"
Languages:
- Language: en
  Versions:
  - Version: 1
    Fields:
    - ID: "8cdc337e-a112-42fb-bbb4-4143751e123f"
      Hint: __Revision
      Value: "2679d03e-2853-4f0a-a644-1456fc734750"
