﻿---
ID: "75335a0d-33a3-4630-8fe5-bcc0d1eb2100"
Parent: "6e4585e8-7dfd-4c51-ae8b-a996673f1aad"
Template: "04646a89-996f-4ee7-878a-ffdbf1f0ef0d"
Path: /sitecore/layout/Renderings/Project/XMCMyaccount/Common/AccountSelector
SharedFields:
- ID: "037fe404-dd19-4bf7-8e30-4dadf68b27b0"
  Hint: componentName
  Value: AccountSelector
- ID: "06d5295c-ed2f-4a54-9bf2-26228d113318"
  Hint: __Icon
  Value: Office/32x32/list_style_numbered.png
- ID: "17bb046a-a32a-41b3-8315-81217947611b"
  Hint: ComponentQuery
  Value: |
    query AccountSelectorQuery($datasource: String!, $language: String!) {
      item(path: $datasource, language: $language) {
        ContractAccountLabel: field(name: "ContractAccountLabel") {
          value
        }
        SwitchAccountLabel: field(name: "SwitchAccountLabel") {
          value
        }
        ServiceAddressLabel: field(name: "ServiceAddressLabel") {
          value
        }
        ChangeAddressLabel: field(name: "ChangeAddressLabel") {
          value
        }
        NoActiveAccountsFoundLabel: field(name: "NoActiveAccountsFoundLabel") {
          value
        }
        SwitchAccountAddressLabel: field(name: "SwitchAccountAddressLabel") {
          value
        }
        ChooseAccountLabel: field(name: "ChooseAccountLabel") {
          value
        }
        SelectAddressLabel: field(name: "SelectAddressLabel") {
          value
        }
        NoActiveEsiidFoundLabel: field(name: "NoActiveEsiidFoundLabel") {
          value
        }
        CutOfDate: field(name: "CutOfDate") {
          value
        }
        SortColumn: field(name: "SortColumn") {
          value
        }
        SortDirection: field(name: "SortDirection") {
          value
        }
        Channel: field(name: "Channel") {
          value
        }
      }
    }
Languages:
- Language: en
  Versions:
  - Version: 1
    Fields:
    - ID: "8cdc337e-a112-42fb-bbb4-4143751e123f"
      Hint: __Revision
      Value: "ec77f1b0-037e-4eff-badd-e486a09a2ff6"
