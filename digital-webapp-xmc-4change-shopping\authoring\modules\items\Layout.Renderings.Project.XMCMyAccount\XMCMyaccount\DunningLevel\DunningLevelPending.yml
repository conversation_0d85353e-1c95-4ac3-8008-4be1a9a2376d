﻿---
ID: "057955b5-02aa-4e4f-9ac7-d81b29e3c89b"
Parent: "a9059596-74fd-478d-a53a-ecde93637775"
Template: "04646a89-996f-4ee7-878a-ffdbf1f0ef0d"
Path: /sitecore/layout/Renderings/Project/XMCMyaccount/DunningLevel/DunningLevelPending
SharedFields:
- ID: "037fe404-dd19-4bf7-8e30-4dadf68b27b0"
  Hint: componentName
  Value: DunningLevel30
- ID: "06d5295c-ed2f-4a54-9bf2-26228d113318"
  Hint: __Icon
  Value: office/32x32/newspaper.png
- ID: "17bb046a-a32a-41b3-8315-81217947611b"
  Hint: ComponentQuery
  Value: |
    query DunningLevel30Query($datasource: String!, $language: String!) {
      item(path: $datasource, language: $language) {
        AccountNumberText: field(name: "AccountNumberText") {
          value
        }
        AccountSummarylink: field(name: "AccountSummarylink") {
          value
        }
        DisconnectDNL50TDU: field(name: "DisconnectDNL50TDU") {
          ... on MultilistField {
            targetItems {
             displayName
            }
          }
        }
        DisconnectOverlay: field(name: "DisconnectOverlay") {
          ... on MultilistField {
            targetItems {
              displayName
             
            }
          }
        }
        DisconnectOverlayMessages: field(name: "DisconnectOverlayMessages") {
          ... on MultilistField {
            targetItems {
              displayName
             
            }
          }
        }
        DisconnectionDateText: field(name: "DisconnectionDateText") {
          value
        }
        DunningButtonLink: field(name: "DunningButtonLink") {
          value
        }
        DunningDisconnectedButtonText: field(name: "DunningDisconnectedButtonText") {
          value
        }
        DunningLevel30ButtonText: field(name: "DunningLevel30ButtonText") {
          value
        }
        DunningLevel50ButtonText: field(name: "DunningLevel50ButtonText") {
          value
        }
        GoToAccountSummaryLinkText: field(name: "GoToAccountSummaryLinkText") {
          value
        }
        HidePopupCloseButton: field(name: "HidePopupCloseButton") {
          value
        }
        OneTimePaymentDNP: field(name: "OneTimePaymentDNP") {
          value
        }
        PastDueAmountText: field(name: "PastDueAmountText") {
          value
        }
        RecurringPaymentTermsAndConditions: field(name: "RecurringPaymentTermsAndConditions") {
          value
        }
        ScheduledPaymentDNP: field(name: "ScheduledPaymentDNP") {
          value
        }
      }
    }
     
    
    
Languages:
- Language: en
  Versions:
  - Version: 1
    Fields:
    - ID: "8cdc337e-a112-42fb-bbb4-4143751e123f"
      Hint: __Revision
      Value: "9a1b2f71-72b2-4b5d-8ec1-95cd36266a37"
