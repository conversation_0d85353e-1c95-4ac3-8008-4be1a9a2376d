﻿---
ID: "b71caf0f-1b6b-407d-ac91-4ef15881790e"
Parent: "5fa50a2b-6fc0-4389-846a-372c76017922"
Template: "04646a89-996f-4ee7-878a-ffdbf1f0ef0d"
Path: /sitecore/layout/Renderings/Project/XMCMyaccount/BillPaymentAndRewards/RedeemRewards
SharedFields:
- ID: "037fe404-dd19-4bf7-8e30-4dadf68b27b0"
  Hint: componentName
  Value: RedeemRewards
- ID: "17bb046a-a32a-41b3-8315-81217947611b"
  Hint: ComponentQuery
  Value: |
    query RedeemRewardsQuery($datasource: String!, $language: String!) {
       item(path: $datasource, language: $language) {
        Title: field(name: "Title") {
          value
        }
        AvailableRewardsBalance: field(name: "AvailableRewardsBalance") {
          value
        }
        RewardAmount: field(name: "RewardAmount") {
          value
        }
        BalanceBeforeRewards: field(name: "BalanceBeforeRewards") {
          value
        }
        BalanceAfterRewards: field(name: "BalanceAfterRewards") {
          value
        }
        RedeemRewardsButton: field(name: "RedeemRewardsButton") {
          value
        }
        CancelRewardsButton: field(name: "CancelRewardsButton") {
          value
        }
        SuccessRedirectionLink: field(name: "SuccessRedirectionLink") {
          jsonValue
        }
        CancelRedirectionLink: field(name: "CancelRedirectionLink") {
          jsonValue
        }
        ErrorMessage: field(name: "ErrorMessage") {
          value
        }
      }
    }
    
- ID: "ba3f86a2-4a1c-4d78-b63d-91c2779c1b5e"
  Hint: __Sortorder
  Value: 200
Languages:
- Language: en
  Versions:
  - Version: 1
    Fields:
    - ID: "8cdc337e-a112-42fb-bbb4-4143751e123f"
      Hint: __Revision
      Value: "87e62ae8-0a35-4ad0-a555-7975d3123826"
