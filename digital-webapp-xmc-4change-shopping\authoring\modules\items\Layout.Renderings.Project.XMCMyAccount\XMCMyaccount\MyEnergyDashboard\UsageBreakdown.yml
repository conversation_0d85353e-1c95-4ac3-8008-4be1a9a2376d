﻿---
ID: "d5b8736c-8a73-4bf4-a3a4-47f558a205be"
Parent: "1df57736-9966-4690-8aec-3d3b685de2ac"
Template: "04646a89-996f-4ee7-878a-ffdbf1f0ef0d"
Path: /sitecore/layout/Renderings/Project/XMCMyaccount/MyEnergyDashboard/UsageBreakdown
SharedFields:
- ID: "037fe404-dd19-4bf7-8e30-4dadf68b27b0"
  Hint: componentName
  Value: UsageBreakdown
- ID: "17bb046a-a32a-41b3-8315-81217947611b"
  Hint: ComponentQuery
  Value: |
    query UsageBreakdownQuery($datasource: String!, $language: String!) {
      item(path: $datasource, language: $language) {
        HomeBreakdownText: field(name: "HomeBreakdownText") {
          value
        }
        LastBillText: field(name: "LastBillText") {
          value
        }
        LastTwelveMonthsText: field(name: "LastTwelveMonthsText") {
          value
        }
        HomeCalculationInfoText: field(name: "HomeCalculationInfoText") {
          value
        }
        HomeCalculationTooltipText: field(name: "HomeCalculationTooltipText") {
          value
        }
        TotalText: field(name: "TotalText") {
          value
        }
        UsagePercentageText: field(name: "UsagePercentageText") {
          value
        }
        CostText: field(name: "CostText") {
          value
        }
        UsageText: field(name: "UsageText") {
          value
        }
        CompareByText: field(name: "CompareByText") {
          value
        }
        NoDataAvailableTitle: field(name: "NoDataAvailableTitle") {
          value
        }
        NoDataAvailableDescription: field(name: "NoDataAvailableDescription") {
          value
        }
        KWhUnitText: field(name: "KWhUnitText") {
          value
        }
        PercentageSymbol: field(name: "PercentageSymbol") {
          value
        }
        CurrencySymbol: field(name: "CurrencySymbol") {
          value
        }
      }
    }
    
Languages:
- Language: en
  Versions:
  - Version: 1
    Fields:
    - ID: "8cdc337e-a112-42fb-bbb4-4143751e123f"
      Hint: __Revision
      Value: "e63bcb48-43a8-4483-8fb8-671e1629cb75"
