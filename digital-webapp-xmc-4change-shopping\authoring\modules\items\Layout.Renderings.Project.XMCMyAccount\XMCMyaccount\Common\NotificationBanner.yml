﻿---
ID: "6a478935-f67d-4f3d-98ab-a94ff17cce06"
Parent: "6e4585e8-7dfd-4c51-ae8b-a996673f1aad"
Template: "04646a89-996f-4ee7-878a-ffdbf1f0ef0d"
Path: /sitecore/layout/Renderings/Project/XMCMyaccount/Common/NotificationBanner
SharedFields:
- ID: "037fe404-dd19-4bf7-8e30-4dadf68b27b0"
  Hint: componentName
  Value: NotificationBanner
- ID: "06d5295c-ed2f-4a54-9bf2-26228d113318"
  Hint: __Icon
  Value: Office/32x32/mail_attachment.png
- ID: "17bb046a-a32a-41b3-8315-81217947611b"
  Hint: ComponentQuery
  Value: |
    query NotificationBannerQuery($datasource: String!, $language: String!) {
       item(path: $datasource, language: $language) {
        BannerHeading: field(name: "BannerHeading") {
          value
        }
        BannerContent: field(name: "BannerContent") {
          value
        }
        IconName: field(name: "IconName") {
          value
        }
        ButtonText: field(name: "ButtonText") {
          value
        }
        ButtonLink: field(name: "ButtonLink") {
          jsonValue
        }
        BannerStyle: field(name: "BannerStyle") {
          jsonValue
        }
        SecondaryButtonText: field(name: "SecondaryButtonText") {
          value
        }
        SecondaryButtonLink: field(name: "SecondaryButtonLink") {
          jsonValue
        }
      }
    }
    
    
- ID: "308e88f5-cd6e-4f8f-bafe-95a47dedefdc"
  Hint: Editable
  Value: 
- ID: "c39a90ce-0035-41bb-90f6-3c8a6ea87797"
  Hint: AddFieldEditorButton
  Value: 
- ID: "f172b787-7b88-4bd5-ae4d-6308e102ef11"
  Hint: Enable Datasource Query
  Value: 
Languages:
- Language: en
  Versions:
  - Version: 1
    Fields:
    - ID: "8cdc337e-a112-42fb-bbb4-4143751e123f"
      Hint: __Revision
      Value: "0d1f6a39-3ae6-49a4-a170-7c873e66b790"
