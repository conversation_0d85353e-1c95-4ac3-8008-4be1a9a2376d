﻿---
ID: "0097fa56-fd31-4094-970c-7087e79fe8c0"
Parent: "1df57736-9966-4690-8aec-3d3b685de2ac"
Template: "04646a89-996f-4ee7-878a-ffdbf1f0ef0d"
Path: /sitecore/layout/Renderings/Project/XMCMyaccount/MyEnergyDashboard/CompareBills
SharedFields:
- ID: "037fe404-dd19-4bf7-8e30-4dadf68b27b0"
  Hint: componentName
  Value: CompareBill
- ID: "17bb046a-a32a-41b3-8315-81217947611b"
  Hint: ComponentQuery
  Value: |
    query CompareBillsQuery($datasource: String!, $language: String!) {
      item(path: $datasource, language: $language) {
        CompareByText: field(name: "CompareByText") {
          value
        }
        MonthsText: field(name: "MonthsText") {
          value
        }
        YearsText: field(name: "YearsText") {
          value
        }
        DetailsText: field(name: "DetailsText") {
          value
        }
        TotalChargesText: field(name: "TotalChargesText") {
          value
        }
        TotalkWhUsedText: field(name: "TotalkWhUsedText") {
          value
        }
        DaysinBillingCycleText: field(name: "DaysinBillingCycleText") {
          value
        }
        AvgOutdoorText: field(name: "AvgOutdoorText") {
          value
        }
        DaysAboveText: field(name: "DaysAboveText") {
          value
        }
        DaysBelowText: field(name: "DaysBelowText") {
          value
        }
        CompareCalculationInfoText: field(name: "CompareCalculationInfoText") {
          value
        }
        MonthlyAvgText: field(name: "MonthlyAvgText") {
          value
        }
        DropDownMonthsToShow: field(name: "DropDownMonthsToShow") {
          value
        }
        CompareBillsText: field(name: "CompareBillsText") {
          value
        }
        NoDataAvailableTitle: field(name: "NoDataAvailableTitle") {
          value
        }
        NoDataAvailableDescription: field(name: "NoDataAvailableDescription") {
          value
        }
        BillAmountText: field(name: "BillAmountText") {
          value
        }
        x: field(name: "x") {
          value
        }
        y: field(name: "y") {
          value
        }
        
        LegendColors: field(name: "LegendColors") {
        jsonValue
        }
    
        MonthList: field(name: "MonthList") {
          jsonValue
        }
      }
    }
    
- ID: "ba3f86a2-4a1c-4d78-b63d-91c2779c1b5e"
  Hint: __Sortorder
  Value: 100
Languages:
- Language: en
  Versions:
  - Version: 1
    Fields:
    - ID: "8cdc337e-a112-42fb-bbb4-4143751e123f"
      Hint: __Revision
      Value: "219c019f-4e6f-45a7-8464-f4355ba01ef3"
