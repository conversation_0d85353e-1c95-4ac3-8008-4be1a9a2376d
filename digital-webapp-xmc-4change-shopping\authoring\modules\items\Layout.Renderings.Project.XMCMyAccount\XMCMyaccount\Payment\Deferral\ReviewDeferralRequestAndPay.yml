﻿---
ID: "06ad0de0-f842-46a3-b49f-94b60319cb9b"
Parent: "7bfdce5d-d1ea-4187-b2a0-87d498276811"
Template: "04646a89-996f-4ee7-878a-ffdbf1f0ef0d"
Path: /sitecore/layout/Renderings/Project/XMCMyaccount/Payment/Deferral/ReviewDeferralRequestAndPay
SharedFields:
- ID: "037fe404-dd19-4bf7-8e30-4dadf68b27b0"
  Hint: componentName
  Value: ReviewDeferralRequestAndPay
- ID: "069a8361-b1cd-437c-8c32-a3be78941446"
  Hint: Placeholders
  Value: "{995DA88F-9AE7-5F82-A0A3-A752442833F9}"
- ID: "06d5295c-ed2f-4a54-9bf2-26228d113318"
  Hint: __Icon
  Value: Office/32x32/flashlight.png
- ID: "17bb046a-a32a-41b3-8315-81217947611b"
  Hint: ComponentQuery
  Value: |
    query ReviewDeferralRequestAndPayQuery($datasource: String!, $language: String!) {
      item(path: $datasource, language: $language) {
        PayButtonText: field(name: "PayButtonText") {
          value
        }
        CancelPaymentText: field(name: "CancelPaymentText") {
          value
        }
        PayRemainingBillByText: field(name: "PayRemainingBillByText") {
          value
        }
        RemainingAmountDueText: field(name: "RemainingAmountDueText") {
          value
        }
        NewDisconnectDateText: field(name: "NewDisconnectDateText") {
          value
        }
        IAgreeText: field(name: "IAgreeText") {
          value
        }
        BackToPaymentText: field(name: "BackToPaymentText") {
          value
        }
      }
    }
    
    
    
- ID: "308e88f5-cd6e-4f8f-bafe-95a47dedefdc"
  Hint: Editable
  Value: 
- ID: "c39a90ce-0035-41bb-90f6-3c8a6ea87797"
  Hint: AddFieldEditorButton
  Value: 
- ID: "f172b787-7b88-4bd5-ae4d-6308e102ef11"
  Hint: Enable Datasource Query
  Value: 
Languages:
- Language: en
  Versions:
  - Version: 1
    Fields:
    - ID: "8cdc337e-a112-42fb-bbb4-4143751e123f"
      Hint: __Revision
      Value: "ef2f5931-48ae-495d-a0c3-479c637deeee"
