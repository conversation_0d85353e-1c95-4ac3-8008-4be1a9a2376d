﻿---
ID: "d0005d90-9d59-42e3-a9f6-30f4a19a0b9b"
Parent: "e13e91f1-a791-4889-ba15-5a8aa1f9aec3"
Template: "04646a89-996f-4ee7-878a-ffdbf1f0ef0d"
Path: /sitecore/layout/Renderings/Project/XMCMyaccount/Payment/PaymentMethod
SharedFields:
- ID: "037fe404-dd19-4bf7-8e30-4dadf68b27b0"
  Hint: componentName
  Value: PaymentMethod
- ID: "06d5295c-ed2f-4a54-9bf2-26228d113318"
  Hint: __Icon
  Value: Office/32x32/password_confirm.png
- ID: "17bb046a-a32a-41b3-8315-81217947611b"
  Hint: ComponentQuery
  Value: |
    query PaymentMethodsQuery($datasource: String!, $language: String!) {
      item(path: $datasource, language: $language) {
        PaymentMethodsSubTitleText: field(name: "PaymentMethodsSubTitleText") {
          value
        }
        AccountEndingText: field(name: "AccountEndingText") {
          value
        }
        CardEndingText: field(name: "CardEndingText") {
          value
        }
        AddPaymentMethodText: field(name: "AddPaymentMethodText") {
          value
        }
        AddCardBtnText: field(name: "AddCardBtnText") {
          value
        }
        AddBankAccountBtnText: field(name: "AddBankAccountBtnText") {
          value
        }
        EnableSplitPayment: field(name: "EnableSplitPayment") {
          value
        }
        SplitPayment: field(name: "SplitPayment") {
          value
        }
        ContinueToPaymentReview: field(name: "ContinueToPaymentReview") {
          value
        }
        CancelPaymentReview: field(name: "CancelPaymentReview") {
          value
        }
        AutoPayPaymentMethodTitleText: field(name: "AutoPayPaymentMethodTitleText") {
          value
        }
        AllowOneTimePayment: field(name: "AllowOneTimePayment") {
          value
        }
        SplitPaymentRedirectionLink: field(name: "SplitPaymentRedirectionLink") {
          jsonValue
        }
        InvalidExpirationDateErrorMessage: field(name: "InvalidExpirationDateErrorMessage") {
          value
        }
        UpdateCardText: field(name: "UpdateCardText") {
          value
        }
        RedirectEditCardLink: field(name: "RedirectEditCardLink") {
          jsonValue
        }
        CardExpiringSoonErrorMessage: field(name: "CardExpiringSoonErrorMessage") {
          value
        }
        EditCardUpdateLink: field(name: "EditCardUpdateLink") {
          jsonValue
        }
      }
    }
    
- ID: "ba3f86a2-4a1c-4d78-b63d-91c2779c1b5e"
  Hint: __Sortorder
  Value: 100
- ID: "dbbbeca1-21c7-4906-9dd2-493c1efa59a2"
  Hint: __Shared revision
  Value: "7bb40940-6d99-41e4-b9f2-6dce4c207247"
Languages:
- Language: en
  Versions:
  - Version: 1
    Fields:
    - ID: "8cdc337e-a112-42fb-bbb4-4143751e123f"
      Hint: __Revision
      Value: "8efc0d30-caae-4604-a5ec-0e6cd4c8f4a8"
