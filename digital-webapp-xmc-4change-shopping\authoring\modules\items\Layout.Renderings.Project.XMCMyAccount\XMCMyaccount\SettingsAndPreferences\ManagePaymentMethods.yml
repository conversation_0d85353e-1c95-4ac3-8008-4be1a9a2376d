﻿---
ID: "6b72d938-0ada-449a-9c87-78d3237052de"
Parent: "2af847b3-5589-4894-a300-1f9659f840fa"
Template: "04646a89-996f-4ee7-878a-ffdbf1f0ef0d"
Path: /sitecore/layout/Renderings/Project/XMCMyaccount/SettingsAndPreferences/ManagePaymentMethods
SharedFields:
- ID: "037fe404-dd19-4bf7-8e30-4dadf68b27b0"
  Hint: componentName
  Value: ManagePaymentMethods
- ID: "06d5295c-ed2f-4a54-9bf2-26228d113318"
  Hint: __Icon
  Value: Office/32x32/money_refund.png
- ID: "17bb046a-a32a-41b3-8315-81217947611b"
  Hint: ComponentQuery
  Value: |
    query ManagePaymentMethodQuery($datasource: String!, $language: String!) {
      item(path: $datasource, language: $language) {
        PaymentMethodsTitleText: field(name: "PaymentMethodsTitleText") {
          value
        }
        CardEndingText: field(name: "CardEndingText") {
          value
        }
        AccountEndingText: field(name: "AccountEndingText") {
          value
        }
        AddCardBtnText: field(name: "AddCardBtnText") {
          value
        }
        AddBankAccountBtnText: field(name: "AddBankAccountBtnText") {
          value
        }
        EditCardLink: field(name: "EditCardLink") {
          value
        }
        EditBankLink: field(name: "EditBankLink") {
          value
        }
        InvalidExpirationDateError: field(name: "InvalidExpirationDateError") {
          value
        }
        ExpireUpdateCardText: field(name: "ExpireUpdateCardText") {
          value
        }
        CardExpiringSoonError: field(name: "CardExpiringSoonError") {
          value
        }
        
      }
    }
Languages:
- Language: en
  Versions:
  - Version: 1
    Fields:
    - ID: "8cdc337e-a112-42fb-bbb4-4143751e123f"
      Hint: __Revision
      Value: "12e5ea57-88ab-42ed-a200-900a882cfddf"
