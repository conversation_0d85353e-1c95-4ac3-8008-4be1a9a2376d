﻿---
ID: "96d4dc62-31bb-4387-a6b9-1f85d50c30b9"
Parent: "e13e91f1-a791-4889-ba15-5a8aa1f9aec3"
Template: "04646a89-996f-4ee7-878a-ffdbf1f0ef0d"
Path: /sitecore/layout/Renderings/Project/XMCMyaccount/Payment/PaymentConfirmation
SharedFields:
- ID: "037fe404-dd19-4bf7-8e30-4dadf68b27b0"
  Hint: componentName
  Value: PaymentConfirmation
- ID: "069a8361-b1cd-437c-8c32-a3be78941446"
  Hint: Placeholders
  Value: "{995DA88F-9AE7-5F82-A0A3-A752442833F9}"
- ID: "06d5295c-ed2f-4a54-9bf2-26228d113318"
  Hint: __Icon
  Value: office/32x32/moneybag_euro.png
- ID: "17bb046a-a32a-41b3-8315-81217947611b"
  Hint: ComponentQuery
  Value: |
    query PaymentconfirmationQuery($datasource: String!, $language: String!) {
      item(path: $datasource, language: $language) {
        ConfirmationNumberText: field(name: "ConfirmationNumberText") {
          value
        }
        PaymentAmountText: field(name: "PaymentAmountText") {
          value
        }
        PaymentDateText: field(name: "PaymentDateText") {
          value
        }
        PaymentMethodText: field(name: "PaymentMethodText") {
          value
        }
        AccountText: field(name: "AccountText") {
          value
        }
        ServiceAddressText: field(name: "ServiceAddressText") {
          value
        }
        AccountBalanceText: field(name: "AccountBalanceText") {
          value
        }
        BackToAccSummaryText: field(name: "BackToAccSummaryText") {
          value
        }
        DateFormat: field(name: "DateFormat") {
          value
        }
        PaymentSuccessfulText: field(name: "PaymentSuccessfulText") {
          value
        }
        MyAccountSummaryLink: field(name: "MyAccountSummaryLink") {
          jsonValue
        }
    	ConfirmationFailedText: field(name: "ConfirmationFailedText") {
          value
        }
    	PaymentFailedText: field(name: "PaymentFailedText") {
          value
        }
      }
    }
- ID: "308e88f5-cd6e-4f8f-bafe-95a47dedefdc"
  Hint: Editable
  Value: 
- ID: "c39a90ce-0035-41bb-90f6-3c8a6ea87797"
  Hint: AddFieldEditorButton
  Value: 
- ID: "f172b787-7b88-4bd5-ae4d-6308e102ef11"
  Hint: Enable Datasource Query
  Value: 
Languages:
- Language: en
  Versions:
  - Version: 1
    Fields:
    - ID: "8cdc337e-a112-42fb-bbb4-4143751e123f"
      Hint: __Revision
      Value: "74662a50-dae3-4491-ade8-46bfd89dbcaf"
