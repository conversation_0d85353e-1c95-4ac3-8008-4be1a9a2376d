﻿---
ID: "86fd2f22-96dd-401c-a8aa-05bc89bc4e85"
Parent: "2af847b3-5589-4894-a300-1f9659f840fa"
Template: "04646a89-996f-4ee7-878a-ffdbf1f0ef0d"
Path: /sitecore/layout/Renderings/Project/XMCMyaccount/SettingsAndPreferences/AddressDetails
SharedFields:
- ID: "037fe404-dd19-4bf7-8e30-4dadf68b27b0"
  Hint: componentName
  Value: AddressDetails
- ID: "06d5295c-ed2f-4a54-9bf2-26228d113318"
  Hint: __Icon
  Value: office/32x32/id_card.png
- ID: "17bb046a-a32a-41b3-8315-81217947611b"
  Hint: ComponentQuery
  Value: |
    query AddressDetailsQuery($datasource: String!, $language: String!) {
      item(path: $datasource, language: $language) {
        heading: field(name: "heading") {
          value
        }
        HouseText: field(name: "HouseText") {
          value
        }
        ServiceAddressText: field(name: "ServiceAddressText") {
          value
        }
        AccountMailingAddressText: field(name: "AccountMailingAddressText") {
          value
        }
        EditButtonText: field(name: "EditButtonText") {
          value
        }
        CancelButtonText: field(name: "CancelButtonText") {
          value
        }
        AccountNicknameText: field(name: "AccountNicknameText") {
          value
        }
        StreetAddressText: field(name: "StreetAddressText") {
          value
        }
        ApartmentNumberText: field(name: "ApartmentNumberText") {
          value
        }
        CityText: field(name: "CityText") {
          value
        }
        StateText: field(name: "StateText") {
          value
        }
        ZipcodeText: field(name: "ZipcodeText") {
          value
        }
        POBoxText: field(name: "POBoxText") {
          value
        }
        SaveChangesButtonText: field(name: "SaveChangesButtonText") {
          value
        }
        CancelChangesButtonText: field(name: "CancelChangesButtonText") {
          value
        }
        MailingAddressState: field(name: "MailingAddressState") {
          ... on MultilistField {
            targetItems {
              displayName
            }
          }
        }
        MailingAddressTooltip: field(name: "MailingAddressTooltip") {
          value
        }
        StreetAddressValidationError: field(name: "StreetAddressValidationError") {
          value
        }
        CityValidationError: field(name: "CityValidationError") {
          value
        }
        StateValidationError: field(name: "StateValidationError") {
          value
        }
        ZipCodeValidationError: field(name: "ZipCodeValidationError") {
          value
        }
        POBoxValidationError: field(name: "POBoxValidationError") {
          value
        }
        POCheckBoxLabel: field(name: "POCheckBoxLabel") {
          value
        }
      }
    }
    
Languages:
- Language: en
  Versions:
  - Version: 1
    Fields:
    - ID: "8cdc337e-a112-42fb-bbb4-4143751e123f"
      Hint: __Revision
      Value: "1949e717-b419-4d68-b62e-085463af288b"
