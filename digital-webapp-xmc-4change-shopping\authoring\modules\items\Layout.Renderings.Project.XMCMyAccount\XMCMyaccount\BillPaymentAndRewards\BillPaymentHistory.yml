﻿---
ID: "4532ab25-e457-48f5-b140-16d1cb064e60"
Parent: "5fa50a2b-6fc0-4389-846a-372c76017922"
Template: "04646a89-996f-4ee7-878a-ffdbf1f0ef0d"
Path: /sitecore/layout/Renderings/Project/XMCMyaccount/BillPaymentAndRewards/BillPaymentHistory
SharedFields:
- ID: "037fe404-dd19-4bf7-8e30-4dadf68b27b0"
  Hint: componentName
  Value: BillPaymentHistory
- ID: "069a8361-b1cd-437c-8c32-a3be78941446"
  Hint: Placeholders
  Value: 
- ID: "17bb046a-a32a-41b3-8315-81217947611b"
  Hint: ComponentQuery
  Value: |
    query BillPaymentHistoryQuery($datasource: String!, $language: String!) {
       item(path: $datasource, language: $language) {
        Date: field(name: "Date") {
          value
        }
        Category: field(name: "Category") {
          value
        }
        Amount: field(name: "Amount") {
          value
        }
        Type: field(name: "Type") {
          value
        }
        Method: field(name: "Method") {
          value
        }
        Status: field(name: "Status") {
          value
        }
        DueDate: field(name: "DueDate") {
          value
        }
        TotalUsage: field(name: "TotalUsage") {
          value
        }
        DaysInBillingCycle: field(name: "DaysInBillingCycle") {
          value
        }
        ViewBill: field(name: "ViewBill") {
          value
        }
        NumberOfRows: field(name: "NumberOfRows") {
          value
        }
        NumberOfBills: field(name: "NumberOfBills") {
          value
        }
        CardEndingText: field(name: "CardEndingText") {
          value
        }
        BankAccountEndingText: field(name: "BankAccountEndingText") {
          value
        }
        DateFormat: field(name: "DateFormat") {
          value
        }
        Unit: field(name: "Unit") {
          value
        }
        Days: field(name: "Days") {
          value
        }
        CancelPayment: field(name: "CancelPayment") {
          value
        }
        ScheduledPayment: field(name: "ScheduledPayment") {
          value
        }
        PaymentText: field(name: "PaymentText") {
          value
        }
        OneTimePayment: field(name: "OneTimePayment") {
          value
        }
        CanceledScheduled: field(name: "CanceledScheduled") {
          value
        }
        CanceledScheduledLink: field(name: "CanceledScheduledLink") {
          jsonValue
        }
        BillCategoryText: field(name: "BillCategoryText") {
          value
        }
        PaymentCategoryText: field(name: "PaymentCategoryText") {
          value
        }
      }
    }
    
Languages:
- Language: en
  Versions:
  - Version: 1
    Fields:
    - ID: "8cdc337e-a112-42fb-bbb4-4143751e123f"
      Hint: __Revision
      Value: "0efbef7b-ddb1-45ec-ba0a-737b523b620e"
