﻿---
ID: "3a07d2b2-d81e-4db7-ad04-e43be52c8e2d"
Parent: "3d431d6b-aa48-4d41-b3c2-62ab33b2401d"
Template: "04646a89-996f-4ee7-878a-ffdbf1f0ef0d"
Path: /sitecore/layout/Renderings/Project/XMCMyaccount/MyAccountSummary/MiniWidgets/ProductWidgets/FreeEVMiles
SharedFields:
- ID: "037fe404-dd19-4bf7-8e30-4dadf68b27b0"
  Hint: componentName
  Value: FreeEVMiles
- ID: "17bb046a-a32a-41b3-8315-81217947611b"
  Hint: ComponentQuery
  Value: |
    query FreeEVMilesQuery($datasource: String!, $language: String!) {
      item(path: $datasource, language: $language) {
        ProductType: field(name: "ProductType") {
          value
        }
        Icon: field(name: "Icon") {
          jsonValue
        }
        Heading: field(name: "Heading") {
          value
        }
        Decription: field(name: "Decription") {
          value
        }
        ButtonText: field(name: "ButtonText") {
          value
        }
        ButtonLink: field(name: "ButtonLink") {
          jsonValue
        }
        MonthSavingText: field(name: "MonthSavingText") {
          value
        }
        FreeMilesText: field(name: "FreeMilesText") {
          value
        }
        FreeEVUsageText: field(name: "FreeEVUsageText") {
          value
        }
        PlanSavingText: field(name: "PlanSavingText") {
          value
        }
        EVNotConnectedTitle: field(name: "EVNotConnectedTitle") {
          value
        }
        EVNotConnectedDescription: field(name: "EVNotConnectedDescription") {
          value
        }
        EVConnectedTitle: field(name: "EVConnectedTitle") {
          value
        }
        EVConnectedDescription: field(name: "EVConnectedDescription") {
          value
        }
        GetStartButtonText: field(name: "GetStartButtonText") {
          value
        }
        GetStartLink: field(name: "GetStartLink") {
          jsonValue
        }
        FordIcon: field(name: "FordIcon") {
          jsonValue
        }
        FordEVNotConnectedTitle: field(name: "FordEVNotConnectedTitle") {
          value
        }
        FordEVNotConnectedDescription: field(name: "FordEVNotConnectedDescription") {
          value
        }
        FordEVConnectedTitle: field(name: "FordEVConnectedTitle") {
          value
        }
        FordEVConnectedDescription: field(name: "FordEVConnectedDescription") {
          value
        }
        FordCarVendor: field(name: "FordCarVendor") {
          value
        }
      }
    }
- ID: "1a7c85e5-dc0b-490d-9187-bb1dbcb4c72f"
  Hint: Datasource Template
  Value: /sitecore/templates/Project/XMCMyaccount/Summary/Product Widgets/FreeEVMiles
- ID: "308e88f5-cd6e-4f8f-bafe-95a47dedefdc"
  Hint: Editable
  Value: 
- ID: "b5b27af1-25ef-405c-87ce-369b3a004016"
  Hint: Datasource Location
  Value: 
- ID: "c39a90ce-0035-41bb-90f6-3c8a6ea87797"
  Hint: AddFieldEditorButton
  Value: 
- ID: "f172b787-7b88-4bd5-ae4d-6308e102ef11"
  Hint: Enable Datasource Query
  Value: 
Languages:
- Language: en
  Versions:
  - Version: 1
    Fields:
    - ID: "8cdc337e-a112-42fb-bbb4-4143751e123f"
      Hint: __Revision
      Value: "5e74566b-32ab-4f40-b72f-c48a03de2558"
