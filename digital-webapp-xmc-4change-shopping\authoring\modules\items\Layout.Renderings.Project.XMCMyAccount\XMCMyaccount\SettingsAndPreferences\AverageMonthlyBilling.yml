﻿---
ID: "d51cad1a-6b9a-45cb-880d-8a3772919320"
Parent: "2af847b3-5589-4894-a300-1f9659f840fa"
Template: "04646a89-996f-4ee7-878a-ffdbf1f0ef0d"
Path: /sitecore/layout/Renderings/Project/XMCMyaccount/SettingsAndPreferences/AverageMonthlyBilling
SharedFields:
- ID: "037fe404-dd19-4bf7-8e30-4dadf68b27b0"
  Hint: componentName
  Value: AverageMonthlyBilling
- ID: "06d5295c-ed2f-4a54-9bf2-26228d113318"
  Hint: __Icon
  Value: Office/32x32/table_selection_column.png
- ID: "17bb046a-a32a-41b3-8315-81217947611b"
  Hint: ComponentQuery
  Value: |
    query AverageMonthlyBillingQuery($datasource: String!, $language: String!) {
      item(path: $datasource, language: $language) {
        AverageMonthlyBillingTitle: field(name: "AverageMonthlyBillingTitle") {
          value
        }
        AverageMonthlyBillingStatusText: field(name: "AverageMonthlyBillingStatusText") {
          value
        }
        AnticipatedBillText: field(name: "AnticipatedBillText") {
          value
        }
        TurnOffAverageBillingButtonText: field(name: "TurnOffAverageBillingButtonText") {
          value
        }
        TurnOnAverageBillingButtonText: field(name: "TurnOnAverageBillingButtonText") {
          value
        }
        HowIsCalculatedText: field(name: "HowIsCalculatedText") {
          value
        }
        HowIsCalculatedDescription: field(name: "HowIsCalculatedDescription") {
          value
        }
        AverageMonthlyBillingOnText: field(name: "AverageMonthlyBillingOnText") {
          value
        }
        AverageMonthlyBillingOffText: field(name: "AverageMonthlyBillingOffText") {
          value
        }
        AverageMonthlyBillingSwitchTitle: field(name: "AverageMonthlyBillingSwitchTitle") {
          value
        }
        AverageMonthlyBillingPopupDescription: field(name: "AverageMonthlyBillingPopupDescription") {
          value
        }
        BillingTurnOnButtonText: field(name: "BillingTurnOnButtonText") {
          value
        }
        AverageMonthlyBillingOffPopupDescription: field(name: "AverageMonthlyBillingOffPopupDescription") {
          value
        }
        BillingTurnOffButtonText: field(name: "BillingTurnOffButtonText") {
          value
        }
        BillingSwitchCancelButtonText: field(name: "BillingSwitchCancelButtonText") {
          value
        }
        AverageMonthlyBillingGraph: field(name: "AverageMonthlyBillingGraph") {
          jsonValue
        }
      }
    }
    
- ID: "308e88f5-cd6e-4f8f-bafe-95a47dedefdc"
  Hint: Editable
  Value: 
- ID: "c39a90ce-0035-41bb-90f6-3c8a6ea87797"
  Hint: AddFieldEditorButton
  Value: 
- ID: "f172b787-7b88-4bd5-ae4d-6308e102ef11"
  Hint: Enable Datasource Query
  Value: 
Languages:
- Language: en
  Versions:
  - Version: 1
    Fields:
    - ID: "8cdc337e-a112-42fb-bbb4-4143751e123f"
      Hint: __Revision
      Value: "7cf54a13-dbfd-4956-9d33-6bde2f409771"
