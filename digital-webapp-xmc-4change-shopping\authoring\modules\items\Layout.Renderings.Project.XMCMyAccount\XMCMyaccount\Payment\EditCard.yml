﻿---
ID: "6629c74c-ba9a-4d03-96f0-6de65d0fb7f8"
Parent: "e13e91f1-a791-4889-ba15-5a8aa1f9aec3"
Template: "04646a89-996f-4ee7-878a-ffdbf1f0ef0d"
Path: /sitecore/layout/Renderings/Project/XMCMyaccount/Payment/EditCard
SharedFields:
- ID: "037fe404-dd19-4bf7-8e30-4dadf68b27b0"
  Hint: componentName
  Value: EditCard
- ID: "06d5295c-ed2f-4a54-9bf2-26228d113318"
  Hint: __Icon
  Value: Office/32x32/money_bill_cut.png
- ID: "17bb046a-a32a-41b3-8315-81217947611b"
  Hint: ComponentQuery
  Value: |
    query EditCardQuery($datasource: String!, $language: String!) {
      item(path: $datasource, language: $language) {
        AddEditCreditOrDebitCardText: field(name: "AddEditCreditOrDebitCardText") {
          value
        }
        CardEndingText: field(name: "CardEndingText") {
          value
        }
        AccountEndingText: field(name: "AccountEndingText") {
          value
        }
        CardDetailsText: field(name: "CardDetailsText") {
          value
        }
        CardNumberText: field(name: "CardNumberText") {
          value
        }
        NameOnCardText: field(name: "NameOnCardText") {
          value
        }
        ExpDateText: field(name: "ExpDateText") {
          value
        }
        SecurityCodeText: field(name: "SecurityCodeText") {
          value
        }
        ZipcodeText: field(name: "ZipcodeText") {
          value
        }
        SavePaymentInfoText: field(name: "SavePaymentInfoText") {
          value
        }
        NicknameText: field(name: "NicknameText") {
          value
        }
        CardDefaultPaymentSavetext: field(name: "CardDefaultPaymentSavetext") {
          value
        }
        BackButton: field(name: "BackButton") {
          value
        }
        BackButtonLink: field(name: "BackButtonLink") {
          jsonValue
        }
        DeletePaymentMethodBtnText: field(name: "DeletePaymentMethodBtnText") {
          value
        }
        CancelChangesText: field(name: "CancelChangesText") {
          value
        }
        SaveChangesText: field(name: "SaveChangesText") {
          value
        }
        ScheduledPaymentErrorText: field(name: "ScheduledPaymentErrorText") {
          value
        }
        AutoPayErrorText: field(name: "AutoPayErrorText") {
          value
        }
        EditTextForApp: field(name: "EditTextForApp") {
          value
        }
        SecurityCodeToolTip: field(name: "SecurityCodeToolTip") {
          value
        }
        DeleteTitle: field(name: "DeleteTitle") {
          value
        }
        DeleteDescription: field(name: "DeleteDescription") {
          value
        }
        DeleteItem: field(name: "DeleteItem") {
          value
        }
        ConfirmText: field(name: "ConfirmText") {
          value
        }
        CancelText: field(name: "CancelText") {
          value
        }
        ExpirationDateErrorMessage: field(name: "ExpirationDateErrorMessage") {
          value
        }
        InvalidNickNameErrorMessage: field(name: "InvalidNickNameErrorMessage") {
          value
        }
        ValidDateErrorMessage: field(name: "ValidDateErrorMessage") {
          value
        }
        SecurityCodeErrorMessage: field(name: "SecurityCodeErrorMessage") {
          value
        }
      }
    }
Languages:
- Language: en
  Versions:
  - Version: 1
    Fields:
    - ID: "8cdc337e-a112-42fb-bbb4-4143751e123f"
      Hint: __Revision
      Value: "ca77ce2d-4c04-473c-8d4c-9ca37378f1c2"
